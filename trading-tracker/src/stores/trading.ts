import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { StorageService, type UserSettings, type TradingRecord, type Statistics } from '@/utils/storage'
import { CalculationService } from '@/utils/calculations'

export const useTradingStore = defineStore('trading', () => {
  // 状态
  const userSettings = ref<UserSettings>(StorageService.getUserSettings())
  const tradingRecords = ref<TradingRecord[]>(StorageService.getTradingRecords())
  const loading = ref(false)

  // 计算属性
  const statistics = computed<Statistics>(() => {
    return CalculationService.calculateStatistics(userSettings.value, tradingRecords.value)
  })

  const balanceCurve = computed(() => {
    return CalculationService.calculateBalanceCurve(userSettings.value, tradingRecords.value)
  })

  const profitLossCurve = computed(() => {
    return CalculationService.calculateProfitLossCurve(tradingRecords.value)
  })

  const monthlyStats = computed(() => {
    return CalculationService.calculateMonthlyStats(tradingRecords.value)
  })

  const recentRecords = computed(() => {
    return [...tradingRecords.value]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 10)
  })

  // 操作方法
  const updateUserSettings = (settings: UserSettings) => {
    userSettings.value = settings
    StorageService.saveUserSettings(settings)
  }

  const addTradingRecord = (record: Omit<TradingRecord, 'id'>) => {
    loading.value = true
    try {
      StorageService.addTradingRecord(record)
      tradingRecords.value = StorageService.getTradingRecords()
    } finally {
      loading.value = false
    }
  }

  const updateTradingRecord = (id: number, record: Partial<TradingRecord>) => {
    loading.value = true
    try {
      StorageService.updateTradingRecord(id, record)
      tradingRecords.value = StorageService.getTradingRecords()
    } finally {
      loading.value = false
    }
  }

  const deleteTradingRecord = (id: number) => {
    loading.value = true
    try {
      StorageService.deleteTradingRecord(id)
      tradingRecords.value = StorageService.getTradingRecords()
    } finally {
      loading.value = false
    }
  }

  const getRecordsByDateRange = (startDate: string, endDate: string) => {
    return CalculationService.getRecordsByDateRange(tradingRecords.value, startDate, endDate)
  }

  const refreshData = () => {
    userSettings.value = StorageService.getUserSettings()
    tradingRecords.value = StorageService.getTradingRecords()
  }

  const clearAllData = () => {
    loading.value = true
    try {
      StorageService.clearAllData()
      userSettings.value = StorageService.getUserSettings()
      tradingRecords.value = []
    } finally {
      loading.value = false
    }
  }

  const exportData = () => {
    const data = {
      settings: userSettings.value,
      records: tradingRecords.value,
      exportDate: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `trading-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const importData = (jsonData: string) => {
    try {
      const data = JSON.parse(jsonData)
      if (data.settings && data.records) {
        userSettings.value = data.settings
        tradingRecords.value = data.records
        StorageService.saveUserSettings(data.settings)
        StorageService.saveTradingRecords(data.records)
        return true
      }
      return false
    } catch (error) {
      console.error('Import data error:', error)
      return false
    }
  }

  // 格式化工具方法
  const formatCurrency = (amount: number, decimals: number = 2) => {
    return CalculationService.formatCurrency(amount, userSettings.value.currency, decimals)
  }

  const formatPercentage = (percentage: number, decimals: number = 2) => {
    return CalculationService.formatPercentage(percentage, decimals)
  }

  return {
    // 状态
    userSettings,
    tradingRecords,
    loading,
    
    // 计算属性
    statistics,
    balanceCurve,
    profitLossCurve,
    monthlyStats,
    recentRecords,
    
    // 方法
    updateUserSettings,
    addTradingRecord,
    updateTradingRecord,
    deleteTradingRecord,
    getRecordsByDateRange,
    refreshData,
    clearAllData,
    exportData,
    importData,
    formatCurrency,
    formatPercentage
  }
})
