<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ref } from 'vue'
import { useTradingStore } from '@/stores/trading'

const tradingStore = useTradingStore()
const activeIndex = ref('1')

const handleSelect = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
</script>

<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <el-header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <el-icon class="logo-icon" size="28">
            <TrendCharts />
          </el-icon>
          <h1 class="app-title">交易记录管理系统</h1>
        </div>

        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-label">当前资金</span>
            <span class="stat-value" :class="{ 'positive': tradingStore.statistics.currentBalance > tradingStore.userSettings.initialBalance }">
              {{ tradingStore.formatCurrency(tradingStore.statistics.currentBalance) }}
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">总盈亏</span>
            <span class="stat-value" :class="{ 'positive': tradingStore.statistics.totalProfitLoss > 0, 'negative': tradingStore.statistics.totalProfitLoss < 0 }">
              {{ tradingStore.formatCurrency(tradingStore.statistics.totalProfitLoss) }}
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">胜率</span>
            <span class="stat-value">{{ tradingStore.formatPercentage(tradingStore.statistics.winRate) }}</span>
          </div>
        </div>
      </div>
    </el-header>

    <!-- 主体内容 -->
    <el-container class="main-container">
      <!-- 侧边导航 -->
      <el-aside class="app-aside">
        <el-menu
          :default-active="activeIndex"
          class="sidebar-menu"
          router
          @select="handleSelect"
        >
          <el-menu-item index="/" route="/">
            <el-icon><Monitor /></el-icon>
            <span>仪表板</span>
          </el-menu-item>
          <el-menu-item index="/records" route="/records">
            <el-icon><Document /></el-icon>
            <span>交易记录</span>
          </el-menu-item>
          <el-menu-item index="/history" route="/history">
            <el-icon><DataAnalysis /></el-icon>
            <span>历史查询</span>
          </el-menu-item>
          <el-menu-item index="/settings" route="/settings">
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主要内容区域 -->
      <el-main class="app-main">
        <RouterView />
      </el-main>
    </el-container>
  </div>
</template>

<style scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  height: 70px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 24px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  color: #fff;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
}

.stat-value.positive {
  color: #67c23a;
}

.stat-value.negative {
  color: #f56c6c;
}

.main-container {
  flex: 1;
  height: calc(100vh - 70px);
}

.app-aside {
  width: 240px !important;
  background: #f8f9fa;
  border-right: 1px solid #e4e7ed;
}

.sidebar-menu {
  border: none;
  background: transparent;
  height: 100%;
}

.sidebar-menu .el-menu-item {
  height: 56px;
  line-height: 56px;
  margin: 4px 12px;
  border-radius: 8px;
  color: #606266;
}

.sidebar-menu .el-menu-item:hover {
  background: #ecf5ff;
  color: #409eff;
}

.sidebar-menu .el-menu-item.is-active {
  background: #409eff;
  color: white;
}

.app-main {
  background: #f5f7fa;
  padding: 24px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .header-stats {
    display: none;
  }

  .app-aside {
    width: 200px !important;
  }

  .app-main {
    padding: 16px;
  }
}
</style>
