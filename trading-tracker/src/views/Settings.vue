<template>
  <div class="settings">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>系统设置</h2>
      <p class="header-desc">配置您的交易参数和系统偏好</p>
    </div>

    <div class="settings-grid">
      <!-- 基础设置 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>基础设置</span>
          </div>
        </template>
        
        <el-form :model="settingsForm" :rules="settingsRules" ref="settingsFormRef" label-width="140px">
          <el-form-item label="初始资金" prop="initialBalance">
            <el-input-number
              v-model="settingsForm.initialBalance"
              :precision="2"
              :min="0"
              :step="100"
              style="width: 100%"
            >
              <template #append>{{ settingsForm.currency }}</template>
            </el-input-number>
            <div class="form-help">设置您的初始投资金额</div>
          </el-form-item>
          
          <el-form-item label="仓位百分比" prop="positionPercentage">
            <el-input-number
              v-model="settingsForm.positionPercentage"
              :precision="1"
              :min="0.1"
              :max="100"
              :step="0.5"
              style="width: 100%"
            >
              <template #append>%</template>
            </el-input-number>
            <div class="form-help">每笔交易使用的资金比例</div>
          </el-form-item>
          
          <el-form-item label="货币单位" prop="currency">
            <el-select v-model="settingsForm.currency" style="width: 100%">
              <el-option label="USDT" value="USDT" />
              <el-option label="USD" value="USD" />
              <el-option label="CNY" value="CNY" />
            </el-select>
            <div class="form-help">选择主要使用的货币单位</div>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveSettings" :loading="loading">
              保存设置
            </el-button>
            <el-button @click="resetSettings">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 当前状态 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>当前状态</span>
          </div>
        </template>
        
        <div class="status-grid">
          <div class="status-item">
            <div class="status-label">当前总资金</div>
            <div class="status-value" :class="{ 'positive': statistics.currentBalance > userSettings.initialBalance }">
              {{ formatCurrency(statistics.currentBalance) }}
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-label">建议仓位</div>
            <div class="status-value highlight">
              {{ formatCurrency(statistics.nextPosition) }}
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-label">累计盈亏</div>
            <div class="status-value" :class="{ 'positive': statistics.totalProfitLoss > 0, 'negative': statistics.totalProfitLoss < 0 }">
              {{ formatCurrency(statistics.totalProfitLoss) }}
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-label">盈亏比例</div>
            <div class="status-value" :class="{ 'positive': statistics.profitLossPercentage > 0, 'negative': statistics.profitLossPercentage < 0 }">
              {{ formatPercentage(statistics.profitLossPercentage) }}
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-label">交易胜率</div>
            <div class="status-value">
              {{ formatPercentage(statistics.winRate) }}
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-label">总交易次数</div>
            <div class="status-value">
              {{ statistics.totalTrades }} 笔
            </div>
          </div>
        </div>
      </el-card>

      <!-- 数据管理 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <el-icon><FolderOpened /></el-icon>
            <span>数据管理</span>
          </div>
        </template>
        
        <div class="data-actions">
          <div class="action-item">
            <div class="action-info">
              <h4>导出数据</h4>
              <p>将所有交易记录和设置导出为JSON文件</p>
            </div>
            <el-button type="primary" @click="exportData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
          
          <div class="action-item">
            <div class="action-info">
              <h4>导入数据</h4>
              <p>从JSON文件导入交易记录和设置</p>
            </div>
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="false"
              accept=".json"
              :on-change="handleFileChange"
            >
              <el-button>
                <el-icon><Upload /></el-icon>
                选择文件
              </el-button>
            </el-upload>
          </div>
          
          <div class="action-item danger">
            <div class="action-info">
              <h4>清空数据</h4>
              <p>删除所有交易记录和设置（不可恢复）</p>
            </div>
            <el-button type="danger" @click="clearAllData">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 系统信息 -->
      <el-card class="settings-card">
        <template #header>
          <div class="card-header">
            <el-icon><InfoFilled /></el-icon>
            <span>系统信息</span>
          </div>
        </template>
        
        <div class="system-info">
          <div class="info-item">
            <span class="info-label">应用版本:</span>
            <span class="info-value">v1.0.0</span>
          </div>
          <div class="info-item">
            <span class="info-label">数据存储:</span>
            <span class="info-value">浏览器本地存储</span>
          </div>
          <div class="info-item">
            <span class="info-label">记录总数:</span>
            <span class="info-value">{{ tradingRecords.length }} 条</span>
          </div>
          <div class="info-item">
            <span class="info-label">最后更新:</span>
            <span class="info-value">{{ lastUpdateTime }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTradingStore } from '@/stores/trading'
import type { UserSettings } from '@/utils/storage'

const tradingStore = useTradingStore()

// 响应式数据
const settingsFormRef = ref()
const uploadRef = ref()
const loading = computed(() => tradingStore.loading)

const settingsForm = ref<UserSettings>({
  initialBalance: 1000,
  positionPercentage: 5,
  currency: 'USDT'
})

// 表单验证规则
const settingsRules = {
  initialBalance: [
    { required: true, message: '请输入初始资金', trigger: 'blur' },
    { type: 'number', min: 0, message: '初始资金不能为负数', trigger: 'blur' }
  ],
  positionPercentage: [
    { required: true, message: '请输入仓位百分比', trigger: 'blur' },
    { type: 'number', min: 0.1, max: 100, message: '仓位百分比应在0.1%-100%之间', trigger: 'blur' }
  ],
  currency: [
    { required: true, message: '请选择货币单位', trigger: 'change' }
  ]
}

// 计算属性
const userSettings = computed(() => tradingStore.userSettings)
const statistics = computed(() => tradingStore.statistics)
const tradingRecords = computed(() => tradingStore.tradingRecords)

const lastUpdateTime = computed(() => {
  if (tradingRecords.value.length === 0) return '暂无数据'
  const latestRecord = tradingRecords.value.reduce((latest, record) => {
    return new Date(record.date) > new Date(latest.date) ? record : latest
  })
  return new Date(latestRecord.date).toLocaleDateString('zh-CN')
})

// 方法
const formatCurrency = (amount: number) => tradingStore.formatCurrency(amount)
const formatPercentage = (percentage: number) => tradingStore.formatPercentage(percentage)

const saveSettings = async () => {
  if (!settingsFormRef.value) return
  
  try {
    await settingsFormRef.value.validate()
    tradingStore.updateUserSettings(settingsForm.value)
    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const resetSettings = () => {
  settingsForm.value = { ...userSettings.value }
}

const exportData = () => {
  tradingStore.exportData()
  ElMessage.success('数据导出成功')
}

const handleFileChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const jsonData = e.target?.result as string
      const success = tradingStore.importData(jsonData)
      if (success) {
        ElMessage.success('数据导入成功')
        settingsForm.value = { ...tradingStore.userSettings }
      } else {
        ElMessage.error('数据格式错误，导入失败')
      }
    } catch (error) {
      ElMessage.error('文件读取失败')
    }
  }
  reader.readAsText(file.raw)
}

const clearAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将永久删除所有数据，是否继续？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    tradingStore.clearAllData()
    settingsForm.value = { ...tradingStore.userSettings }
    ElMessage.success('数据清空成功')
  } catch {
    // 用户取消操作
  }
}

onMounted(() => {
  settingsForm.value = { ...userSettings.value }
})
</script>

<style scoped>
.settings {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.settings-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.status-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.status-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.status-value.positive {
  color: #67c23a;
}

.status-value.negative {
  color: #f56c6c;
}

.status-value.highlight {
  color: #409eff;
}

.data-actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.action-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.action-item.danger:hover {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.action-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.action-info p {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #606266;
  font-size: 14px;
}

.info-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-upload) {
  width: auto;
}

@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-item {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .action-info {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
}
</style>
