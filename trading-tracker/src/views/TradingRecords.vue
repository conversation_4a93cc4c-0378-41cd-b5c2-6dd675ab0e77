<template>
  <div class="trading-records">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-left">
        <h2>交易记录管理</h2>
        <p class="header-desc">记录和管理您的每日交易数据</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加记录
        </el-button>
        <el-button @click="exportRecords">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <div class="filter-row">
        <div class="filter-item">
          <label>日期范围:</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </div>
        <div class="filter-item">
          <label>盈亏筛选:</label>
          <el-select v-model="profitFilter" placeholder="选择盈亏类型" @change="applyFilters">
            <el-option label="全部" value="all" />
            <el-option label="盈利" value="profit" />
            <el-option label="亏损" value="loss" />
          </el-select>
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置筛选</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table 
        :data="filteredRecords" 
        style="width: 100%"
        :loading="loading"
        empty-text="暂无交易记录"
      >
        <el-table-column prop="date" label="日期" width="120" sortable />
        <el-table-column prop="profitLoss" label="盈亏 (USDT)" width="140" sortable>
          <template #default="scope">
            <span :class="{ 'profit': scope.row.profitLoss > 0, 'loss': scope.row.profitLoss < 0 }">
              {{ scope.row.profitLoss >= 0 ? '+' : '' }}{{ scope.row.profitLoss.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="deposit" label="入金 (USDT)" width="120">
          <template #default="scope">
            {{ scope.row.deposit.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="withdrawal" label="出金 (USDT)" width="120">
          <template #default="scope">
            {{ scope.row.withdrawal.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="withdrawalCNY" label="出金 (CNY)" width="120">
          <template #default="scope">
            {{ scope.row.withdrawalCNY.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="trades" label="交易次数" width="100" />
        <el-table-column prop="wins" label="胜利次数" width="100" />
        <el-table-column label="胜率" width="100">
          <template #default="scope">
            <el-tag 
              :type="getWinRateTagType(scope.row.trades > 0 ? (scope.row.wins / scope.row.trades) * 100 : 0)"
              size="small"
            >
              {{ scope.row.trades > 0 ? ((scope.row.wins / scope.row.trades) * 100).toFixed(1) + '%' : '0%' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="note" label="备注" min-width="150" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="editRecord(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="deleteRecord(scope.row)" class="delete-btn">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog 
      :title="editingRecord ? '编辑交易记录' : '添加交易记录'"
      v-model="showAddDialog"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="recordForm" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="日期" prop="date">
          <el-date-picker
            v-model="recordForm.date"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="盈亏 (USDT)" prop="profitLoss">
          <el-input-number
            v-model="recordForm.profitLoss"
            :precision="2"
            :step="10"
            style="width: 100%"
            placeholder="输入盈亏金额"
          />
        </el-form-item>
        <el-form-item label="入金 (USDT)" prop="deposit">
          <el-input-number
            v-model="recordForm.deposit"
            :precision="2"
            :min="0"
            :step="10"
            style="width: 100%"
            placeholder="输入入金金额"
          />
        </el-form-item>
        <el-form-item label="出金 (USDT)" prop="withdrawal">
          <el-input-number
            v-model="recordForm.withdrawal"
            :precision="2"
            :min="0"
            :step="10"
            style="width: 100%"
            placeholder="输入出金金额"
          />
        </el-form-item>
        <el-form-item label="出金 (CNY)" prop="withdrawalCNY">
          <el-input-number
            v-model="recordForm.withdrawalCNY"
            :precision="2"
            :min="0"
            :step="100"
            style="width: 100%"
            placeholder="输入人民币出金金额"
          />
        </el-form-item>
        <el-form-item label="交易次数" prop="trades">
          <el-input-number
            v-model="recordForm.trades"
            :min="0"
            :step="1"
            style="width: 100%"
            placeholder="输入交易次数"
          />
        </el-form-item>
        <el-form-item label="胜利次数" prop="wins">
          <el-input-number
            v-model="recordForm.wins"
            :min="0"
            :max="recordForm.trades"
            :step="1"
            style="width: 100%"
            placeholder="输入胜利次数"
          />
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input
            v-model="recordForm.note"
            type="textarea"
            :rows="3"
            placeholder="输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveRecord" :loading="loading">
            {{ editingRecord ? '更新' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTradingStore } from '@/stores/trading'
import type { TradingRecord } from '@/utils/storage'

const tradingStore = useTradingStore()

// 响应式数据
const showAddDialog = ref(false)
const editingRecord = ref<TradingRecord | null>(null)
const dateRange = ref<[string, string] | null>(null)
const profitFilter = ref('all')
const loading = computed(() => tradingStore.loading)

// 表单数据
const recordForm = ref({
  date: new Date().toISOString().split('T')[0],
  profitLoss: 0,
  deposit: 0,
  withdrawal: 0,
  withdrawalCNY: 0,
  trades: 0,
  wins: 0,
  note: ''
})

// 表单验证规则
const formRules = {
  date: [{ required: true, message: '请选择日期', trigger: 'change' }],
  profitLoss: [{ required: true, message: '请输入盈亏金额', trigger: 'blur' }],
  trades: [{ required: true, message: '请输入交易次数', trigger: 'blur' }],
  wins: [{ required: true, message: '请输入胜利次数', trigger: 'blur' }]
}

const formRef = ref()

// 计算属性
const filteredRecords = computed(() => {
  let records = [...tradingStore.tradingRecords]
  
  // 日期筛选
  if (dateRange.value) {
    const [startDate, endDate] = dateRange.value
    records = records.filter(record => {
      const recordDate = new Date(record.date)
      const start = new Date(startDate)
      const end = new Date(endDate)
      return recordDate >= start && recordDate <= end
    })
  }
  
  // 盈亏筛选
  if (profitFilter.value === 'profit') {
    records = records.filter(record => record.profitLoss > 0)
  } else if (profitFilter.value === 'loss') {
    records = records.filter(record => record.profitLoss < 0)
  }
  
  // 按日期倒序排列
  return records.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
})

// 方法
const getWinRateTagType = (winRate: number) => {
  if (winRate >= 70) return 'success'
  if (winRate >= 50) return 'warning'
  return 'danger'
}

const handleDateRangeChange = () => {
  applyFilters()
}

const applyFilters = () => {
  // 筛选逻辑已在计算属性中处理
}

const resetFilters = () => {
  dateRange.value = null
  profitFilter.value = 'all'
}

const editRecord = (record: TradingRecord) => {
  editingRecord.value = record
  recordForm.value = { ...record }
  showAddDialog.value = true
}

const deleteRecord = async (record: TradingRecord) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条交易记录吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    tradingStore.deleteTradingRecord(record.id)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const saveRecord = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (editingRecord.value) {
      tradingStore.updateTradingRecord(editingRecord.value.id, recordForm.value)
      ElMessage.success('更新成功')
    } else {
      tradingStore.addTradingRecord(recordForm.value)
      ElMessage.success('添加成功')
    }
    
    showAddDialog.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const resetForm = () => {
  editingRecord.value = null
  recordForm.value = {
    date: new Date().toISOString().split('T')[0],
    profitLoss: 0,
    deposit: 0,
    withdrawal: 0,
    withdrawalCNY: 0,
    trades: 0,
    wins: 0,
    note: ''
  }
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const exportRecords = () => {
  tradingStore.exportData()
  ElMessage.success('数据导出成功')
}

onMounted(() => {
  tradingStore.refreshData()
})
</script>

<style scoped>
.trading-records {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
}

.profit {
  color: #67c23a;
  font-weight: 600;
}

.loss {
  color: #f56c6c;
  font-weight: 600;
}

.delete-btn {
  color: #f56c6c !important;
}

.delete-btn:hover {
  color: #f78989 !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px;
  border-top: 1px solid #f0f0f0;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-item label {
    font-weight: 600;
  }
}
</style>
