<template>
  <div class="dashboard">
    <!-- 统计卡片区域 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon balance">
            <el-icon size="24"><Wallet /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">当前总资金</div>
            <div class="stat-number" :class="{ 'positive': statistics.currentBalance > userSettings.initialBalance }">
              {{ formatCurrency(statistics.currentBalance) }}
            </div>
            <div class="stat-desc">
              初始资金: {{ formatCurrency(userSettings.initialBalance) }}
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon profit" :class="{ 'negative': statistics.totalProfitLoss < 0 }">
            <el-icon size="24"><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">累计盈亏</div>
            <div class="stat-number" :class="{ 'positive': statistics.totalProfitLoss > 0, 'negative': statistics.totalProfitLoss < 0 }">
              {{ formatCurrency(statistics.totalProfitLoss) }}
            </div>
            <div class="stat-desc">
              盈亏比例: {{ formatPercentage(statistics.profitLossPercentage) }}
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon position">
            <el-icon size="24"><PieChart /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">建议仓位</div>
            <div class="stat-number">
              {{ formatCurrency(statistics.nextPosition) }}
            </div>
            <div class="stat-desc">
              仓位比例: {{ formatPercentage(userSettings.positionPercentage) }}
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon winrate">
            <el-icon size="24"><Trophy /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">交易胜率</div>
            <div class="stat-number">
              {{ formatPercentage(statistics.winRate) }}
            </div>
            <div class="stat-desc">
              {{ statistics.totalWins }}/{{ statistics.totalTrades }} 笔交易
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>资金曲线</span>
            <el-button type="text" @click="refreshCharts">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>
        <div class="chart-container">
          <BalanceChart :data="balanceCurve" />
        </div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>盈亏趋势</span>
            <el-button type="text" @click="refreshCharts">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>
        <div class="chart-container">
          <ProfitLossChart :data="profitLossCurve" />
        </div>
      </el-card>
    </div>

    <!-- 最近交易记录 -->
    <el-card class="recent-trades">
      <template #header>
        <div class="card-header">
          <span>最近交易记录</span>
          <el-button type="primary" size="small" @click="$router.push('/records')">
            查看全部
          </el-button>
        </div>
      </template>
      <el-table :data="recentRecords" style="width: 100%">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="profitLoss" label="盈亏" width="120">
          <template #default="scope">
            <span :class="{ 'positive': scope.row.profitLoss > 0, 'negative': scope.row.profitLoss < 0 }">
              {{ formatCurrency(scope.row.profitLoss) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="trades" label="交易次数" width="100" />
        <el-table-column prop="wins" label="胜利次数" width="100" />
        <el-table-column label="胜率" width="100">
          <template #default="scope">
            {{ scope.row.trades > 0 ? formatPercentage((scope.row.wins / scope.row.trades) * 100) : '0%' }}
          </template>
        </el-table-column>
        <el-table-column prop="note" label="备注" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useTradingStore } from '@/stores/trading'
import BalanceChart from '@/components/charts/BalanceChart.vue'
import ProfitLossChart from '@/components/charts/ProfitLossChart.vue'

const tradingStore = useTradingStore()

const statistics = computed(() => tradingStore.statistics)
const userSettings = computed(() => tradingStore.userSettings)
const balanceCurve = computed(() => tradingStore.balanceCurve)
const profitLossCurve = computed(() => tradingStore.profitLossCurve)
const recentRecords = computed(() => tradingStore.recentRecords)

const formatCurrency = (amount: number) => tradingStore.formatCurrency(amount)
const formatPercentage = (percentage: number) => tradingStore.formatPercentage(percentage)

const refreshCharts = () => {
  tradingStore.refreshData()
}
</script>

<style scoped>
.dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.balance {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.profit {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.profit.negative {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.stat-icon.position {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.winrate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-number.positive {
  color: #67c23a;
}

.stat-number.negative {
  color: #f56c6c;
}

.stat-desc {
  font-size: 12px;
  color: #c0c4cc;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
}

.chart-container {
  height: 300px;
  padding: 16px 0;
}

.recent-trades {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.positive {
  color: #67c23a;
}

.negative {
  color: #f56c6c;
}

@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-content {
    padding: 4px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
  }

  .stat-number {
    font-size: 20px;
  }
}
</style>
