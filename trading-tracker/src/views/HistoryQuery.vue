<template>
  <div class="history-query">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>历史数据查询</h2>
      <p class="header-desc">查看和分析您的历史交易数据</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <el-icon><Search /></el-icon>
          <span>查询条件</span>
        </div>
      </template>
      
      <div class="query-form">
        <div class="query-row">
          <div class="query-item">
            <label>时间范围:</label>
            <el-date-picker
              v-model="queryForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleQuery"
            />
          </div>
          
          <div class="query-item">
            <label>统计维度:</label>
            <el-select v-model="queryForm.dimension" @change="handleQuery">
              <el-option label="按日统计" value="daily" />
              <el-option label="按周统计" value="weekly" />
              <el-option label="按月统计" value="monthly" />
            </el-select>
          </div>
          
          <div class="query-item">
            <el-button type="primary" @click="handleQuery">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetQuery">重置</el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 统计概览 -->
    <div class="stats-overview" v-if="queryResults.length > 0">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon profit">
            <el-icon size="24"><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">期间总盈亏</div>
            <div class="stat-number" :class="{ 'positive': periodStats.totalProfitLoss > 0, 'negative': periodStats.totalProfitLoss < 0 }">
              {{ formatCurrency(periodStats.totalProfitLoss) }}
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon trades">
            <el-icon size="24"><DataLine /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">交易次数</div>
            <div class="stat-number">{{ periodStats.totalTrades }}</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon winrate">
            <el-icon size="24"><Trophy /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">期间胜率</div>
            <div class="stat-number">{{ formatPercentage(periodStats.winRate) }}</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon average">
            <el-icon size="24"><PieChart /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">平均盈亏</div>
            <div class="stat-number" :class="{ 'positive': periodStats.avgProfitLoss > 0, 'negative': periodStats.avgProfitLoss < 0 }">
              {{ formatCurrency(periodStats.avgProfitLoss) }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表展示 -->
    <div class="charts-section" v-if="queryResults.length > 0">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>{{ getDimensionLabel() }}盈亏趋势</span>
            <el-button type="text" @click="exportChart">
              <el-icon><Download /></el-icon>
              导出图表
            </el-button>
          </div>
        </template>
        <div class="chart-container">
          <HistoryChart :data="chartData" :dimension="queryForm.dimension" />
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card" v-if="queryResults.length > 0">
      <template #header>
        <div class="card-header">
          <span>{{ getDimensionLabel() }}数据详情</span>
          <el-button type="primary" size="small" @click="exportTable">
            <el-icon><Download /></el-icon>
            导出表格
          </el-button>
        </div>
      </template>
      
      <el-table :data="queryResults" style="width: 100%">
        <el-table-column :label="getDimensionLabel()" width="120">
          <template #default="scope">
            {{ formatPeriod(scope.row.period) }}
          </template>
        </el-table-column>
        <el-table-column prop="profitLoss" label="盈亏" width="140" sortable>
          <template #default="scope">
            <span :class="{ 'positive': scope.row.profitLoss > 0, 'negative': scope.row.profitLoss < 0 }">
              {{ scope.row.profitLoss >= 0 ? '+' : '' }}{{ formatCurrency(scope.row.profitLoss) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="trades" label="交易次数" width="100" sortable />
        <el-table-column prop="wins" label="胜利次数" width="100" sortable />
        <el-table-column label="胜率" width="100" sortable>
          <template #default="scope">
            <el-tag 
              :type="getWinRateTagType(scope.row.trades > 0 ? (scope.row.wins / scope.row.trades) * 100 : 0)"
              size="small"
            >
              {{ scope.row.trades > 0 ? formatPercentage((scope.row.wins / scope.row.trades) * 100) : '0%' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="deposit" label="入金" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.deposit) }}
          </template>
        </el-table-column>
        <el-table-column prop="withdrawal" label="出金" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.withdrawal) }}
          </template>
        </el-table-column>
        <el-table-column label="平均盈亏" width="120">
          <template #default="scope">
            <span :class="{ 'positive': scope.row.avgProfitLoss > 0, 'negative': scope.row.avgProfitLoss < 0 }">
              {{ formatCurrency(scope.row.avgProfitLoss) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 空状态 -->
    <el-empty v-if="queryResults.length === 0 && hasQueried" description="没有找到符合条件的数据">
      <el-button type="primary" @click="resetQuery">重新查询</el-button>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useTradingStore } from '@/stores/trading'
import HistoryChart from '@/components/charts/HistoryChart.vue'
import type { TradingRecord } from '@/utils/storage'

const tradingStore = useTradingStore()

// 响应式数据
const queryForm = ref({
  dateRange: null as [string, string] | null,
  dimension: 'monthly' as 'daily' | 'weekly' | 'monthly'
})

const queryResults = ref<any[]>([])
const hasQueried = ref(false)

// 计算属性
const periodStats = computed(() => {
  if (queryResults.value.length === 0) {
    return {
      totalProfitLoss: 0,
      totalTrades: 0,
      totalWins: 0,
      winRate: 0,
      avgProfitLoss: 0
    }
  }

  const totalProfitLoss = queryResults.value.reduce((sum, item) => sum + item.profitLoss, 0)
  const totalTrades = queryResults.value.reduce((sum, item) => sum + item.trades, 0)
  const totalWins = queryResults.value.reduce((sum, item) => sum + item.wins, 0)
  const winRate = totalTrades > 0 ? (totalWins / totalTrades) * 100 : 0
  const avgProfitLoss = queryResults.value.length > 0 ? totalProfitLoss / queryResults.value.length : 0

  return {
    totalProfitLoss,
    totalTrades,
    totalWins,
    winRate,
    avgProfitLoss
  }
})

const chartData = computed(() => {
  return queryResults.value.map(item => ({
    period: item.period,
    profitLoss: item.profitLoss,
    cumulative: item.cumulative || 0
  }))
})

// 方法
const formatCurrency = (amount: number) => tradingStore.formatCurrency(amount)
const formatPercentage = (percentage: number) => tradingStore.formatPercentage(percentage)

const getDimensionLabel = () => {
  const labels = {
    daily: '日期',
    weekly: '周',
    monthly: '月份'
  }
  return labels[queryForm.value.dimension]
}

const formatPeriod = (period: string) => {
  if (queryForm.value.dimension === 'weekly') {
    return `第${period}周`
  }
  return period
}

const getWinRateTagType = (winRate: number) => {
  if (winRate >= 70) return 'success'
  if (winRate >= 50) return 'warning'
  return 'danger'
}

const handleQuery = () => {
  hasQueried.value = true
  
  let records = [...tradingStore.tradingRecords]
  
  // 日期筛选
  if (queryForm.value.dateRange) {
    const [startDate, endDate] = queryForm.value.dateRange
    records = tradingStore.getRecordsByDateRange(startDate, endDate)
  }
  
  // 按维度聚合数据
  queryResults.value = aggregateByDimension(records, queryForm.value.dimension)
}

const aggregateByDimension = (records: TradingRecord[], dimension: string) => {
  const grouped: { [key: string]: TradingRecord[] } = {}
  
  records.forEach(record => {
    const date = new Date(record.date)
    let key: string
    
    switch (dimension) {
      case 'daily':
        key = record.date
        break
      case 'weekly':
        const weekNumber = getWeekNumber(date)
        key = `${date.getFullYear()}-W${weekNumber}`
        break
      case 'monthly':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        break
      default:
        key = record.date
    }
    
    if (!grouped[key]) {
      grouped[key] = []
    }
    grouped[key].push(record)
  })
  
  // 聚合统计
  const results = Object.entries(grouped).map(([period, periodRecords]) => {
    const profitLoss = periodRecords.reduce((sum, r) => sum + r.profitLoss, 0)
    const deposit = periodRecords.reduce((sum, r) => sum + r.deposit, 0)
    const withdrawal = periodRecords.reduce((sum, r) => sum + r.withdrawal, 0)
    const trades = periodRecords.reduce((sum, r) => sum + r.trades, 0)
    const wins = periodRecords.reduce((sum, r) => sum + r.wins, 0)
    const avgProfitLoss = periodRecords.length > 0 ? profitLoss / periodRecords.length : 0
    
    return {
      period,
      profitLoss,
      deposit,
      withdrawal,
      trades,
      wins,
      avgProfitLoss,
      recordCount: periodRecords.length
    }
  })
  
  // 排序
  return results.sort((a, b) => a.period.localeCompare(b.period))
}

const getWeekNumber = (date: Date) => {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1)
  const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7)
}

const resetQuery = () => {
  queryForm.value = {
    dateRange: null,
    dimension: 'monthly'
  }
  queryResults.value = []
  hasQueried.value = false
}

const exportChart = () => {
  ElMessage.success('图表导出功能开发中')
}

const exportTable = () => {
  if (queryResults.value.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }
  
  // 简单的CSV导出
  const headers = ['时间', '盈亏', '交易次数', '胜利次数', '胜率', '入金', '出金', '平均盈亏']
  const csvContent = [
    headers.join(','),
    ...queryResults.value.map(row => [
      formatPeriod(row.period),
      row.profitLoss,
      row.trades,
      row.wins,
      row.trades > 0 ? ((row.wins / row.trades) * 100).toFixed(2) + '%' : '0%',
      row.deposit,
      row.withdrawal,
      row.avgProfitLoss.toFixed(2)
    ].join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `历史数据_${queryForm.value.dimension}_${new Date().toISOString().split('T')[0]}.csv`
  link.click()
  
  ElMessage.success('表格导出成功')
}

onMounted(() => {
  // 默认查询最近3个月的数据
  const endDate = new Date()
  const startDate = new Date()
  startDate.setMonth(startDate.getMonth() - 3)
  
  queryForm.value.dateRange = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]
  
  handleQuery()
})
</script>

<style scoped>
.history-query {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.query-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.query-form {
  padding: 8px 0;
}

.query-row {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.query-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.query-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.profit {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.trades {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.winrate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.average {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-number.positive {
  color: #67c23a;
}

.stat-number.negative {
  color: #f56c6c;
}

.charts-section {
  margin: 8px 0;
}

.chart-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
}

.chart-container {
  height: 400px;
  padding: 16px 0;
}

.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
}

.positive {
  color: #67c23a;
  font-weight: 600;
}

.negative {
  color: #f56c6c;
  font-weight: 600;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-empty) {
  padding: 60px 0;
}

@media (max-width: 768px) {
  .query-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .query-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .query-item label {
    font-weight: 600;
  }

  .stats-overview {
    grid-template-columns: 1fr;
  }

  .stat-content {
    padding: 4px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
  }

  .stat-number {
    font-size: 20px;
  }

  .chart-container {
    height: 300px;
  }
}
</style>
