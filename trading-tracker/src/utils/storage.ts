// LocalStorage 工具类
export interface UserSettings {
  initialBalance: number;
  positionPercentage: number;
  currency: string;
}

export interface TradingRecord {
  id: number;
  date: string;
  profitLoss: number;
  deposit: number;
  withdrawal: number;
  withdrawalCNY: number;
  trades: number;
  wins: number;
  note: string;
}

export interface Statistics {
  currentBalance: number;
  totalProfitLoss: number;
  totalDeposit: number;
  totalWithdrawal: number;
  totalWithdrawalCNY: number;
  profitLossPercentage: number;
  winRate: number;
  nextPosition: number;
  totalTrades: number;
  totalWins: number;
}

const STORAGE_KEYS = {
  USER_SETTINGS: 'trading_tracker_settings',
  TRADING_RECORDS: 'trading_tracker_records'
};

// 默认设置
const DEFAULT_SETTINGS: UserSettings = {
  initialBalance: 1000,
  positionPercentage: 5,
  currency: 'USDT'
};

export class StorageService {
  // 获取用户设置
  static getUserSettings(): UserSettings {
    try {
      const settings = localStorage.getItem(STORAGE_KEYS.USER_SETTINGS);
      return settings ? JSON.parse(settings) : DEFAULT_SETTINGS;
    } catch (error) {
      console.error('Error loading user settings:', error);
      return DEFAULT_SETTINGS;
    }
  }

  // 保存用户设置
  static saveUserSettings(settings: UserSettings): void {
    try {
      localStorage.setItem(STORAGE_KEYS.USER_SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving user settings:', error);
    }
  }

  // 获取交易记录
  static getTradingRecords(): TradingRecord[] {
    try {
      const records = localStorage.getItem(STORAGE_KEYS.TRADING_RECORDS);
      return records ? JSON.parse(records) : [];
    } catch (error) {
      console.error('Error loading trading records:', error);
      return [];
    }
  }

  // 保存交易记录
  static saveTradingRecords(records: TradingRecord[]): void {
    try {
      localStorage.setItem(STORAGE_KEYS.TRADING_RECORDS, JSON.stringify(records));
    } catch (error) {
      console.error('Error saving trading records:', error);
    }
  }

  // 添加交易记录
  static addTradingRecord(record: Omit<TradingRecord, 'id'>): void {
    const records = this.getTradingRecords();
    const newRecord: TradingRecord = {
      ...record,
      id: Date.now()
    };
    records.push(newRecord);
    this.saveTradingRecords(records);
  }

  // 更新交易记录
  static updateTradingRecord(id: number, record: Partial<TradingRecord>): void {
    const records = this.getTradingRecords();
    const index = records.findIndex(r => r.id === id);
    if (index !== -1) {
      records[index] = { ...records[index], ...record };
      this.saveTradingRecords(records);
    }
  }

  // 删除交易记录
  static deleteTradingRecord(id: number): void {
    const records = this.getTradingRecords();
    const filteredRecords = records.filter(r => r.id !== id);
    this.saveTradingRecords(filteredRecords);
  }

  // 清空所有数据
  static clearAllData(): void {
    localStorage.removeItem(STORAGE_KEYS.USER_SETTINGS);
    localStorage.removeItem(STORAGE_KEYS.TRADING_RECORDS);
  }
}
