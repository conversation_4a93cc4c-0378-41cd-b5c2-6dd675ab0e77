import type { UserSettings, TradingRecord, Statistics } from './storage';

export class CalculationService {
  // 计算统计数据
  static calculateStatistics(settings: UserSettings, records: TradingRecord[]): Statistics {
    const totalProfitLoss = records.reduce((sum, record) => sum + record.profitLoss, 0);
    const totalDeposit = records.reduce((sum, record) => sum + record.deposit, 0);
    const totalWithdrawal = records.reduce((sum, record) => sum + record.withdrawal, 0);
    const totalWithdrawalCNY = records.reduce((sum, record) => sum + record.withdrawalCNY, 0);
    const totalTrades = records.reduce((sum, record) => sum + record.trades, 0);
    const totalWins = records.reduce((sum, record) => sum + record.wins, 0);

    // 当前总资金 = 初始资金 + 累计盈亏 + 总入金 - 总出金
    const currentBalance = settings.initialBalance + totalProfitLoss + totalDeposit - totalWithdrawal;
    
    // 盈亏百分比（相对初始资金）
    const profitLossPercentage = settings.initialBalance > 0 
      ? (totalProfitLoss / settings.initialBalance) * 100 
      : 0;
    
    // 胜率
    const winRate = totalTrades > 0 ? (totalWins / totalTrades) * 100 : 0;
    
    // 下一单仓位 = 当前总资金 × 仓位百分比
    const nextPosition = currentBalance * (settings.positionPercentage / 100);

    return {
      currentBalance,
      totalProfitLoss,
      totalDeposit,
      totalWithdrawal,
      totalWithdrawalCNY,
      profitLossPercentage,
      winRate,
      nextPosition,
      totalTrades,
      totalWins
    };
  }

  // 获取日期范围内的记录
  static getRecordsByDateRange(records: TradingRecord[], startDate: string, endDate: string): TradingRecord[] {
    return records.filter(record => {
      const recordDate = new Date(record.date);
      const start = new Date(startDate);
      const end = new Date(endDate);
      return recordDate >= start && recordDate <= end;
    });
  }

  // 计算累计资金曲线数据
  static calculateBalanceCurve(settings: UserSettings, records: TradingRecord[]): Array<{date: string, balance: number}> {
    const sortedRecords = [...records].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    const curve: Array<{date: string, balance: number}> = [];
    
    let runningBalance = settings.initialBalance;
    
    // 添加初始点
    if (sortedRecords.length > 0) {
      curve.push({
        date: sortedRecords[0].date,
        balance: runningBalance
      });
    }

    sortedRecords.forEach(record => {
      runningBalance += record.profitLoss + record.deposit - record.withdrawal;
      curve.push({
        date: record.date,
        balance: runningBalance
      });
    });

    return curve;
  }

  // 计算盈亏曲线数据
  static calculateProfitLossCurve(records: TradingRecord[]): Array<{date: string, profitLoss: number, cumulative: number}> {
    const sortedRecords = [...records].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    const curve: Array<{date: string, profitLoss: number, cumulative: number}> = [];
    
    let cumulativePL = 0;

    sortedRecords.forEach(record => {
      cumulativePL += record.profitLoss;
      curve.push({
        date: record.date,
        profitLoss: record.profitLoss,
        cumulative: cumulativePL
      });
    });

    return curve;
  }

  // 格式化货币显示
  static formatCurrency(amount: number, currency: string = 'USDT', decimals: number = 2): string {
    const formatted = amount.toFixed(decimals);
    return `${formatted} ${currency}`;
  }

  // 格式化百分比显示
  static formatPercentage(percentage: number, decimals: number = 2): string {
    return `${percentage.toFixed(decimals)}%`;
  }

  // 计算月度统计
  static calculateMonthlyStats(records: TradingRecord[]): Array<{month: string, profitLoss: number, trades: number, winRate: number}> {
    const monthlyData: {[key: string]: {profitLoss: number, trades: number, wins: number}} = {};

    records.forEach(record => {
      const date = new Date(record.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {profitLoss: 0, trades: 0, wins: 0};
      }
      
      monthlyData[monthKey].profitLoss += record.profitLoss;
      monthlyData[monthKey].trades += record.trades;
      monthlyData[monthKey].wins += record.wins;
    });

    return Object.entries(monthlyData).map(([month, data]) => ({
      month,
      profitLoss: data.profitLoss,
      trades: data.trades,
      winRate: data.trades > 0 ? (data.wins / data.trades) * 100 : 0
    })).sort((a, b) => a.month.localeCompare(b.month));
  }
}
