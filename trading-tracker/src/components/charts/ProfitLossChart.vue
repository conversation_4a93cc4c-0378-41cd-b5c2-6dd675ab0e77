<template>
  <div class="chart-wrapper">
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
)

interface ProfitLossData {
  date: string
  profitLoss: number
  cumulative: number
}

const props = defineProps<{
  data: ProfitLossData[]
}>()

const chartCanvas = ref<HTMLCanvasElement>()
let chartInstance: ChartJS | null = null

const createChart = () => {
  if (!chartCanvas.value || !props.data.length) return

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  // 销毁现有图表
  if (chartInstance) {
    chartInstance.destroy()
  }

  const labels = props.data.map(item => {
    const date = new Date(item.date)
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  })

  const dailyProfitLoss = props.data.map(item => item.profitLoss)
  const cumulativeProfitLoss = props.data.map(item => item.cumulative)

  chartInstance = new ChartJS(ctx, {
    type: 'bar',
    data: {
      labels,
      datasets: [
        {
          type: 'bar',
          label: '当日盈亏',
          data: dailyProfitLoss,
          backgroundColor: dailyProfitLoss.map(value => 
            value >= 0 ? 'rgba(103, 194, 58, 0.6)' : 'rgba(245, 108, 108, 0.6)'
          ),
          borderColor: dailyProfitLoss.map(value => 
            value >= 0 ? 'rgb(103, 194, 58)' : 'rgb(245, 108, 108)'
          ),
          borderWidth: 1,
          borderRadius: 4,
          yAxisID: 'y'
        },
        {
          type: 'line',
          label: '累计盈亏',
          data: cumulativeProfitLoss,
          borderColor: 'rgb(102, 126, 234)',
          backgroundColor: 'rgba(102, 126, 234, 0.1)',
          borderWidth: 3,
          fill: false,
          tension: 0.4,
          pointBackgroundColor: 'rgb(102, 126, 234)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 5,
          pointHoverRadius: 8,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        title: {
          display: false
        },
        legend: {
          display: true,
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#fff',
          bodyColor: '#fff',
          borderColor: 'rgb(102, 126, 234)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            title: (context) => {
              const index = context[0].dataIndex
              return props.data[index]?.date || ''
            },
            label: (context) => {
              const value = context.parsed.y
              if (context.datasetIndex === 0) {
                return `当日盈亏: ${value >= 0 ? '+' : ''}${value.toFixed(2)} USDT`
              } else {
                return `累计盈亏: ${value >= 0 ? '+' : ''}${value.toFixed(2)} USDT`
              }
            }
          }
        }
      },
      scales: {
        x: {
          display: true,
          grid: {
            display: false
          },
          ticks: {
            color: '#909399',
            font: {
              size: 12
            }
          }
        },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          grid: {
            color: 'rgba(0, 0, 0, 0.05)'
          },
          ticks: {
            color: '#909399',
            font: {
              size: 12
            },
            callback: function(value) {
              return value + ' USDT'
            }
          },
          title: {
            display: true,
            text: '当日盈亏',
            color: '#909399'
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          grid: {
            drawOnChartArea: false
          },
          ticks: {
            color: '#909399',
            font: {
              size: 12
            },
            callback: function(value) {
              return value + ' USDT'
            }
          },
          title: {
            display: true,
            text: '累计盈亏',
            color: '#909399'
          }
        }
      }
    }
  })
}

onMounted(() => {
  createChart()
})

watch(() => props.data, () => {
  createChart()
}, { deep: true })

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})
</script>

<style scoped>
.chart-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

canvas {
  max-height: 100%;
  max-width: 100%;
}
</style>
